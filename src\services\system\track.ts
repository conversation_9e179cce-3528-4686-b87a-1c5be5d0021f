import { get } from '../api';

// 学员练习计划跟踪响应类型（根据 openapi.json 定义）
export interface TrackExercise {
  e_id: number; // 练习ID
  e_title: string; // 练习标题
  e_type: number; // 练习类型（1：作业单；2：角色扮演）
  e_pic?: string | null; // 练习图片URL
  e_intro?: string | null; // 练习简介
  e_duration?: number | null; // 练习时长（分钟）
  t_id?: number | null; // 老师ID
  t_name?: string | null; // 老师姓名
  t_avatar?: string | null; // 老师头像URL
  t_intro?: string | null; // 老师简介
  depend: number; // 练习依赖（0：不依赖；1：依赖）
  w_id?: number | null; // 作业单ID
  s_id?: number | null; // 场景ID
  el_id?: number | null; // 练习情况ID
  el_status?: number | null; // 练习状态（0：待练习；1：练习中；2：已提交）
  el_btime?: string | null; // 开始练习时间
  el_stime?: string | null; // 提交练习时间
  el_utime?: string | null; // 上次更新时间
}

export interface TrackExerciseListResponse {
  class_name: string; // 班级名称
  exercises: TrackExercise[]; // 练习列表
}

// 理论模块接口
export interface TrackModuleResponse {
  name: string;
}

// 理论框架接口
export interface TrackFrameworkResponse {
  name: string;
  logo?: string | null;
  module_list: TrackModuleResponse[];
}

// 作答指南接口
export interface TrackGuideResponse {
  title: string;
  details: string;
}

// 问题响应接口
export interface TrackQuestionResponse {
  id: number;
  title: string;
  bgtext?: string | null;
  bgvideo?: string | null;
  draft?: string | null;
  answer?: string | null;
  comment?: string | null;
  framework_list?: TrackFrameworkResponse[];
  guide_list?: TrackGuideResponse[];
}

// 单元响应接口
export interface TrackUnitResponse {
  id: number;
  name: string;
  bgtext?: string | null;
  bgvideo?: string | null;
}

// 作业单基本信息响应接口
export interface TrackWorksheetBasicResponse {
  title: string;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  report?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number;
  eid: number;
  elid?: number | null;
  tid?: number | null;
  tname?: string | null;
  tavatar?: string | null;
}

// 作业单单元列表响应接口
export interface TrackWorksheetUnitsResponse {
  unit_list: TrackUnitResponse[];
}

// 单元问题响应接口
export interface TrackUnitQuestionsResponse {
  question_list: TrackQuestionResponse[];
}

// 场景角色信息接口
export interface TrackSceneCharacter {
  id: number;
  cid: number;
  name: string;
  gender: number;
  avatar?: string | null;
  profile?: string | null;
  played: number; // 0: AI扮演, 1: 学员扮演
}

// 场景发言信息接口
export interface TrackSceneSpeech {
  id: number;
  cid: number;
  played: number; // 0: AI扮演, 1: 学员扮演
  content: string;
  to_cids?: string | null; // @提及的角色ID列表
  ctime: string;
}

// 场景基本信息响应接口
export interface TrackSceneBasicResponse {
  title: string;
  pic?: string | null;
  intro?: string | null;
  duration?: number | null;
  bgtext?: string | null;
  bgvideo?: string | null;
  btime?: string | null;
  stime?: string | null;
  utime?: string | null;
  status: number; // 练习状态（0：待练习；1：练习中；2：已提交）
  eid: number;
  elid?: number | null;
  tid?: number | null;
  tname?: string | null;
  tavatar?: string | null;
  characters: TrackSceneCharacter[];
  speeches: TrackSceneSpeech[];
  comments?: string | null; // AI老师点评
}

// 获取学员练习计划跟踪数据
export const getTrackExercises = (
  clazzId: number,
  studentId: number,
  tenantId: number
): Promise<TrackExerciseListResponse> => {
  return get(`/sys/track/${clazzId}/${studentId}/exercises`, { tenant_id: tenantId });
};

// 获取作业单基本信息
export const getTrackWorksheet = (
  wid: number,
  classId: number,
  studentId: number,
  tenantId: number
): Promise<TrackWorksheetBasicResponse> => {
  return get(`/sys/track/worksheet/${wid}`, {
    class_id: classId,
    student_id: studentId,
    tenant_id: tenantId
  });
};

// 获取作业单单元列表
export const getTrackWorksheetUnits = (
  wid: number,
  tenantId: number
): Promise<TrackWorksheetUnitsResponse> => {
  return get(`/sys/track/worksheet/${wid}/units`, { tenant_id: tenantId });
};

// 获取单元问题列表
export const getTrackUnitQuestions = (
  wid: number,
  uid: number,
  elid: number,
  tenantId: number
): Promise<TrackUnitQuestionsResponse> => {
  return get(`/sys/track/worksheet/${wid}/unit/${uid}/questions`, {
    elid: elid,
    tenant_id: tenantId
  });
};

// 获取场景基本信息
export const getTrackScene = (
  sceneId: number,
  classId: number,
  studentId: number,
  tenantId: number
): Promise<TrackSceneBasicResponse> => {
  return get(`/sys/track/scene/${sceneId}`, {
    class_id: classId,
    student_id: studentId,
    tenant_id: tenantId
  });
};