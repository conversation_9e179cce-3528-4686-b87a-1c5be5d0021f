import React, { useEffect, useState, useCallback } from 'react';
import { Card, Breadcrumb, Spin, Typography, Empty, Layout, Avatar, List } from 'antd';
import { HomeOutlined, UserOutlined, RobotOutlined } from '@ant-design/icons';
import { Link, useParams } from 'react-router-dom';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  getTrackScene,
  type TrackSceneBasicResponse,
  type TrackSceneCharacter
} from '../../../services/system/track';
import { getClazz, getClazzStudents, type ClazzResponse, type ClassStudentWithStudentInfoResponse } from '../../../services/system/clazz';
import { showError } from '../../../utils/errorHandler';
import { getGlobalTenantInfo } from '../../../components/GlobalTenantSelector';

const { Title, Text } = Typography;
const { Sider, Content } = Layout;

// Markdown 渲染组件
const MarkdownRenderer: React.FC<{ content: string; className?: string }> = ({ content, className }) => {
  if (!content || !content.trim()) {
    return <Text type="secondary">暂无内容</Text>;
  }

  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // 自定义表格样式
          table: ({ children }) => (
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              border: '1px solid #d9d9d9',
              marginBottom: '16px'
            }}>
              {children}
            </table>
          ),
          th: ({ children }) => (
            <th style={{
              border: '1px solid #d9d9d9',
              padding: '8px 12px',
              backgroundColor: '#fafafa',
              fontWeight: 'bold',
              textAlign: 'left'
            }}>
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td style={{
              border: '1px solid #d9d9d9',
              padding: '8px 12px'
            }}>
              {children}
            </td>
          ),
          // 自定义代码块样式
          code: ({ children, className, ...props }) => {
            const isInline = !className || !className.includes('language-');
            if (isInline) {
              return (
                <code
                  style={{
                    backgroundColor: '#f5f5f5',
                    padding: '2px 4px',
                    borderRadius: '3px',
                    fontSize: '0.9em',
                    fontFamily: 'Monaco, Consolas, "Courier New", monospace'
                  }}
                  {...props}
                >
                  {children}
                </code>
              );
            }
            return (
              <pre style={{
                backgroundColor: '#f5f5f5',
                padding: '12px',
                borderRadius: '6px',
                overflow: 'auto',
                fontSize: '0.9em',
                fontFamily: 'Monaco, Consolas, "Courier New", monospace'
              }}>
                <code {...props}>{children}</code>
              </pre>
            );
          },
          // 自定义引用样式
          blockquote: ({ children }) => (
            <blockquote style={{
              borderLeft: '4px solid #d9d9d9',
              paddingLeft: '16px',
              margin: '16px 0',
              color: '#666',
              fontStyle: 'italic'
            }}>
              {children}
            </blockquote>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul style={{ paddingLeft: '20px', marginBottom: '16px' }}>
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol style={{ paddingLeft: '20px', marginBottom: '16px' }}>
              {children}
            </ol>
          ),
          // 自定义段落样式
          p: ({ children }) => (
            <p style={{ marginBottom: '12px', lineHeight: '1.6' }}>
              {children}
            </p>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 style={{ fontSize: '1.5em', fontWeight: 'bold', marginBottom: '16px', marginTop: '24px' }}>
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 style={{ fontSize: '1.3em', fontWeight: 'bold', marginBottom: '12px', marginTop: '20px' }}>
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 style={{ fontSize: '1.1em', fontWeight: 'bold', marginBottom: '8px', marginTop: '16px' }}>
              {children}
            </h3>
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

const TrackScene: React.FC = () => {
  const { classId, studentId, sceneId } = useParams();
  const classIdNum = classId ? Number(classId) : undefined;
  const studentIdNum = studentId ? Number(studentId) : undefined;
  const sceneIdNum = sceneId ? Number(sceneId) : undefined;

  const [sceneData, setSceneData] = useState<TrackSceneBasicResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [clazzInfo, setClazzInfo] = useState<ClazzResponse | null>(null);
  const [studentInfo, setStudentInfo] = useState<ClassStudentWithStudentInfoResponse | null>(null);

  // 获取班级信息
  const fetchClazzInfo = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum) return;
      const res = await getClazz(classIdNum, tenantInfo.id);
      setClazzInfo(res);
    } catch (e) {
      showError(e, '获取班级信息失败');
    }
  }, [classIdNum]);

  // 获取学员信息
  const fetchStudentInfo = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !classIdNum || !studentIdNum) return;

      const res = await getClazzStudents(classIdNum, tenantInfo.id, { active: 1, limit: 100 });
      const student = res.items.find(s => s.sid === studentIdNum);
      setStudentInfo(student || null);
    } catch (e) {
      showError(e, '获取学员信息失败');
    }
  }, [classIdNum, studentIdNum]);

  // 获取场景基本信息
  const fetchSceneData = useCallback(async () => {
    try {
      const tenantInfo = getGlobalTenantInfo();
      if (!tenantInfo || !sceneIdNum || !classIdNum || !studentIdNum) return;

      const res = await getTrackScene(sceneIdNum, classIdNum, studentIdNum, tenantInfo.id);
      setSceneData(res);
    } catch (e) {
      showError(e, '获取场景信息失败');
    }
  }, [sceneIdNum, classIdNum, studentIdNum]);

  // 获取所有数据
  const fetchAllData = useCallback(async () => {
    try {
      setLoading(true);
      await Promise.all([
        fetchClazzInfo(),
        fetchStudentInfo(),
        fetchSceneData()
      ]);
    } catch (e) {
      showError(e, '获取数据失败');
    } finally {
      setLoading(false);
    }
  }, [fetchClazzInfo, fetchStudentInfo, fetchSceneData]);

  useEffect(() => {
    fetchAllData();
  }, [fetchAllData]);

  // 获取状态标签
  const getStatusTag = (status: number) => {
    switch (status) {
      case 0:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>待练习</span>;
      case 1:
        return <span style={{ color: '#21808d', backgroundColor: '#e6fffb', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>练习中</span>;
      case 2:
        return <span style={{ color: '#13343b', backgroundColor: '#f6ffed', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>已提交</span>;
      default:
        return <span style={{ color: '#8c8c8c', backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '4px', fontSize: '12px' }}>未开始</span>;
    }
  };

  // 渲染老师信息
  const renderTeacherInfo = () => {
    if (!sceneData?.tname) return null;

    return (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {sceneData.tavatar ? (
          <Avatar
            src={sceneData.tavatar}
            size={20}
            style={{ marginRight: '4px' }}
          />
        ) : (
          <UserOutlined style={{ marginRight: '4px' }} />
        )}
        <Text type="secondary">指导老师: {sceneData.tname}</Text>
      </div>
    );
  };

  // 获取角色头像
  const getCharacterAvatar = (character: TrackSceneCharacter) => {
    if (character.avatar) {
      return <Avatar src={character.avatar} size={40} />;
    }
    return <Avatar icon={<UserOutlined />} size={40} />;
  };

  // 根据发言者获取角色信息
  const getCharacterById = (cid: number) => {
    return sceneData?.characters.find((c) => c.cid === cid);
  };

  // 格式化时间显示
  const formatTime = (timeString: string) => {
    const date = new Date(timeString);
    return date.toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: 'calc(100vh - 150px)' }}>
      {/* 面包屑导航 */}
      <Breadcrumb
        style={{ marginBottom: 12, flexShrink: 0 }}
        items={[
          {
            title: <Link to="/system/home"><HomeOutlined /> 主页</Link>,
          },
          {
            title: '租户空间',
          },
          {
            title: <Link to="/system/track">教学跟踪</Link>,
          },
          {
            title: <Link to={`/system/track/exercises/${classId}?studentId=${studentId}`}>{clazzInfo?.name || '班级练习跟踪'}</Link>,
          },
          {
            title: sceneData?.title || '场景详情',
          },
        ]}
      />

      {loading ? (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          minHeight: '80vh'
        }}>
          <Spin size="large" />
          <div style={{ marginTop: '16px' }}>
            <Text type="secondary">正在加载场景信息...</Text>
          </div>
        </div>
      ) : sceneData ? (
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* 顶部基本信息部分 */}
          <div style={{ marginBottom: '16px' }}>
            <Card>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px', marginBottom: '12px' }}>
                <Title level={2} style={{ margin: 0 }}>
                  {sceneData.title}
                </Title>
                {getStatusTag(sceneData.status)}
              </div>

              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
                  {renderTeacherInfo()}

                  {sceneData.duration && (
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Text type="secondary">预计时长: {sceneData.duration} 分钟</Text>
                    </div>
                  )}
                </div>

                {/* 学员信息 */}
                {studentInfo && (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar
                      icon={<UserOutlined />}
                      size={20}
                      style={{ marginRight: '8px', backgroundColor: '#1677ff' }}
                    />
                    <Text strong>{studentInfo.name}（ID: {studentInfo.sid}）</Text>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* 下半部分 - 左右布局 */}
          <Layout style={{
            flex: 1,
            boxShadow: '0 4px 12px rgba(38, 38, 38, 0.15)',
            borderRadius: '12px',
            overflow: 'hidden'
          }}>
            {/* 左侧角色列表 */}
            <Sider
              width={240}
              style={{
                background: '#fff',
                borderRight: '1px solid #f0f0f0'
              }}
            >
              <div style={{ padding: '16px' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Title level={4} style={{ margin: 0 }}>参会者</Title>
                </div>

                <List
                  dataSource={sceneData.characters}
                  renderItem={(character) => (
                    <List.Item style={{ padding: '8px 0', border: 'none' }}>
                      <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <div style={{ marginRight: '12px' }}>
                          {getCharacterAvatar(character)}
                        </div>
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                            {character.name}
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                            {character.played === 1 ? (
                              <>
                                <UserOutlined style={{ fontSize: '12px', color: '#1677ff' }} />
                                <Text type="secondary" style={{ fontSize: '12px' }}>学员扮演</Text>
                              </>
                            ) : (
                              <>
                                <RobotOutlined style={{ fontSize: '12px', color: '#722ed1' }} />
                                <Text type="secondary" style={{ fontSize: '12px' }}>AI扮演</Text>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </div>
            </Sider>

            {/* 右侧聊天历史 */}
            <Layout>
              <Content style={{
                padding: '24px',
                background: '#fff',
                overflow: 'auto'
              }}>
                <div style={{ marginBottom: '18px' }}>
                  <Title level={3}>发言历史</Title>
                </div>

                {sceneData.speeches && sceneData.speeches.length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {sceneData.speeches.map((speech) => {
                      const character = getCharacterById(speech.cid);
                      const isStudent = speech.played === 1;

                      return (
                        <div key={speech.id} style={{
                          display: 'flex',
                          gap: '12px',
                          flexDirection: isStudent ? 'row-reverse' : 'row',
                          alignItems: 'flex-start'
                        }}>
                          <div style={{ flexShrink: 0 }}>
                            {character ? getCharacterAvatar(character) : (
                              <Avatar icon={<UserOutlined />} size={40} />
                            )}
                          </div>
                          <div style={{
                            flex: 1,
                            maxWidth: '70%',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: isStudent ? 'flex-end' : 'flex-start'
                          }}>
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              marginBottom: '4px',
                              flexDirection: isStudent ? 'row-reverse' : 'row'
                            }}>
                              <Text strong>{character?.name || '未知用户'}</Text>
                              {isStudent ? (
                                <UserOutlined style={{ fontSize: '12px', color: '#1677ff' }} />
                              ) : (
                                <RobotOutlined style={{ fontSize: '12px', color: '#722ed1' }} />
                              )}
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {formatTime(speech.ctime)}
                              </Text>
                            </div>
                            <div style={{
                              padding: '12px',
                              backgroundColor: isStudent ? '#e6f4ff' : '#f9f0ff',
                              borderRadius: isStudent ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                              border: `1px solid ${isStudent ? '#91caff' : '#d3adf7'}`,
                              maxWidth: '100%',
                              wordBreak: 'break-word'
                            }}>
                              <MarkdownRenderer content={speech.content} />
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <Empty description="暂无发言记录" />
                )}

                {/* AI老师点评 */}
                {sceneData.comments && (
                  <div style={{ marginTop: '24px' }}>
                    <Title level={4}>AI老师点评</Title>
                    <div style={{
                      padding: '16px',
                      backgroundColor: '#fff7e6',
                      borderRadius: '8px',
                      border: '1px solid #ffd591'
                    }}>
                      <MarkdownRenderer content={sceneData.comments} />
                    </div>
                  </div>
                )}
              </Content>
            </Layout>
          </Layout>
        </div>
      ) : (
        <Empty
          description="暂无场景信息"
          style={{ padding: '50px' }}
        />
      )}
    </div>
  );
};

export default TrackScene;